# Task ID: 19
# Title: Optimize Random Question Pool Selection Performance
# Status: pending
# Dependencies: 18, 5, 2
# Priority: medium
# Description: Implement a caching strategy for random question pool results using WorksheetDocumentCacheService, optimize database query performance via indexing on key fields (subject, parentSubject, grade, type, status, difficultyLevel), manage MongoDB connection pooling, and establish performance monitoring with metrics collection for the question selection process.
# Details:
This task focuses on enhancing the performance and scalability of the random question selection mechanism from the question pool.

**1. Caching Strategy Implementation (using `WorksheetDocumentCacheService`):**
   - Target the `getRandomQuestions` method in `QuestionPoolService` (from Task 16, enhanced by Task 18) for caching.
   - Integrate with the `WorksheetDocumentCacheService` (assume available or provided). Define any necessary adaptations if question objects require special handling by the cache service.
   - Develop a robust cache key generation strategy. Keys must uniquely identify a query based on all its parameters: `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, `count`, and any distribution rules (from Task 18).
   - Implement logic to populate the cache with successful, non-empty results from `getRandomQuestions`.
   - Implement cache invalidation strategies:
     - **Time-To-Live (TTL):** Configure suitable TTLs for cached question sets based on content volatility.
     - **Event-driven (Optional):** If feasible, explore invalidating or updating specific cache entries when underlying questions in the pool are created, updated, or deleted, particularly if they match criteria of cached queries.

**2. Database Indexing Optimization (MongoDB):**
   - Analyze query patterns from the MongoDB aggregation pipeline in `getRandomQuestions` (Task 16/18).
   - Identify and create optimal compound indexes on the `questions` collection to support filtering, sorting, and aggregation stages. Fields for consideration include `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, and `status`.
   - Example indexes (to be refined based on actual query analysis):
     - `{ "subjectId": 1, "gradeLevel": 1, "questionType": 1, "status": 1 }`
     - `{ "parentSubjectId": 1, "gradeLevel": 1, "status": 1 }`
     - `{ "difficultyLevel": 1, "status": 1, "questionType": 1 }`
   - Utilize MongoDB's `explain("executionStats")` command extensively to verify index usage (e.g., presence of `IXSCAN` stages, minimized `docsExamined`) and effectiveness for various query combinations.

**3. Connection Pooling Management (MongoDB Driver):**
   - Review and fine-tune MongoDB driver connection pool settings within the NestJS application's database configuration.
   - Adjust parameters like `maxPoolSize`, `minPoolSize`, `maxIdleTimeMS`, and `waitQueueTimeoutMS` to align with expected application load, concurrency, and performance requirements.
   - The goal is to ensure efficient connection reuse, minimize connection latency, and prevent connection exhaustion under peak loads.

**4. Performance Monitoring and Metrics Collection:**
   - Integrate a metrics collection library (e.g., `prom-client` for Prometheus) or leverage an APM tool compatible with NestJS.
   - Instrument the `QuestionPoolService.getRandomQuestions` method to capture and expose key performance indicators (KPIs):
     - Overall execution time (average, p95, p99).
     - Cache hit rate and miss rate for `WorksheetDocumentCacheService` related to question pool queries.
     - Database query execution time (if separable).
     - Number of questions requested versus number of questions returned.
     - Error rates specific to the question selection operation.
   - Expose these metrics (e.g., via a `/metrics` endpoint for Prometheus scraping) or ensure they are pushed to a centralized monitoring dashboard.
   - Establish baseline performance targets and consider configuring alerts for significant deviations (e.g., sustained high latency, critically low cache hit rate).

# Test Strategy:
**1. Caching Verification:**
   - **Unit Tests:** Validate the cache key generation logic for correctness and uniqueness under various input parameter combinations.
   - **Integration Tests:**
     - Invoke `getRandomQuestions` multiple times with identical parameters. Assert that subsequent calls are significantly faster and (by mocking or inspecting the cache service) confirm that results are served from the cache.
     - Verify that altering any query parameter results in a cache miss and triggers a fresh database query.
     - Test cache invalidation: Modify a question in the database that should affect a cached result set. Re-query and assert that fresh data is returned. Verify TTL-based expiration.

**2. Database Indexing Verification:**
   - **Baseline Measurement:** Before applying new indexes, execute representative `getRandomQuestions` queries and record their performance using `explain("executionStats")` (note `docsExamined`, execution time, stages used).
   - **Post-Indexing Validation:** After creating indexes, re-run the same queries. Use `explain("executionStats")` to confirm that the new indexes are being utilized effectively (e.g., `IXSCAN` is used, `docsExamined` is reduced, query plans are more efficient).
   - **Load Testing:** Conduct targeted load tests on the `getRandomQuestions` endpoint before and after indexing to quantify the improvement in query response times and throughput under concurrent load.

**3. Connection Pooling Verification:**
   - **Monitoring:** During load tests, monitor MongoDB server connection statistics (`db.serverStatus().connections`) to observe the number of active, idle, and available connections. Ensure these numbers stay within the configured pool limits and that the pool behaves as expected (e.g., connections are reused).
   - **Stress Test:** Simulate high concurrency scenarios targeting `getRandomQuestions` to verify that the application manages database connections gracefully without connection timeout errors or pool exhaustion issues.

**4. Performance Monitoring Verification:**
   - **Metrics Validation:** Manually trigger various scenarios for `getRandomQuestions` (e.g., cache hits, cache misses, successful queries, queries resulting in errors). Verify that all configured metrics (latency, cache hit/miss ratio, error counts, etc.) are accurately generated and reported to the monitoring system or endpoint.
   - **Dashboard/Alerting Check (if applicable):** Confirm that metrics are correctly displayed on dashboards and that alerts trigger based on predefined thresholds when simulated conditions meet alert criteria.

**5. Overall System Performance:**
   - **End-to-End Load Testing:** Conduct comprehensive load tests simulating realistic user traffic patterns that heavily utilize the `getRandomQuestions` functionality.
   - **Comparative Analysis:** Measure and compare key performance metrics (overall throughput, response time percentiles, error rates) before and after all optimizations (caching, indexing, connection pooling) are implemented to demonstrate the cumulative positive impact.
