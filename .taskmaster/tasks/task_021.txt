# Task ID: 21
# Title: Implement Comprehensive Error Handling for Random Question Pool Selection
# Status: pending
# Dependencies: 4, 14, 16, 17, 19, 20
# Priority: medium
# Description: Implement robust error handling and recovery mechanisms for the random question pool selection process, covering database connectivity, insufficient content, AI service failures, and WebSocket communication.
# Details:
1. **MongoDB Connection Retry Logic:** Modify `QuestionPoolService` or the underlying data access layer to implement retry logic for MongoDB operations. Configure for 3 retry attempts with exponential backoff upon connection failures or transient errors during question retrieval. Log each attempt and the final status.
2. **Insufficient Questions Graceful Degradation & AI Fallback:** Enhance the question sourcing logic (likely in `WorksheetGenerateConsumer` or an orchestrator service) to handle scenarios where the question pool (Task 16) returns fewer questions than requested. If a deficit occurs, trigger AI question generation (using services from Task 4, potentially orchestrated by logic from Task 17) to fulfill the remaining count. This fallback should be configurable (Task 20).
3. **AI Service Failure Chain:** Implement a resilient AI interaction layer (e.g., a new `AiOrchestrationService`). This service will attempt to generate questions sequentially: first from OpenAI, then upon failure, from Google AI. If both AI services fail, it will attempt to retrieve questions from a 'Cached Content' source as a final fallback. Log the outcome of each attempt and the service ultimately used.
4. **WebSocket Error Communication:** Integrate with the WebSocket gateway (Task 14) to communicate errors effectively to the client. Define specific error event types (e.g., `worksheet:generation:error`, `database:unavailable`, `ai:service:failed`, `insufficient_questions:no_fallback`) and structured error payloads (error code, message, details). Ensure errors from all parts of the question selection and generation pipeline are caught and propagated correctly.
5. **Comprehensive Logging:** Implement detailed logging using NestJS `LoggerService` for all error events, retry attempts, fallback invocations, and AI service interactions to facilitate debugging and monitoring.

# Test Strategy:
1. **MongoDB Connection Failure:** Simulate MongoDB unavailability (e.g., stop service, invalid connection string). Verify that the system attempts reconnection 3 times with backoff (check logs) and eventually communicates a DB error via WebSocket if unsuccessful.
2. **Insufficient Questions & AI Fallback:** Configure the question pool with fewer questions than a test request. Verify the system identifies the deficit, logs it, and successfully triggers the AI fallback to generate the missing questions. Mock AI services to confirm they are called with correct parameters. The final question set should meet the requested count.
3. **AI Service Failure Chain:** 
    a. Simulate OpenAI API failure (e.g., mock endpoint to return error, invalid API key). Verify the system logs the failure and automatically attempts to use Google AI. 
    b. Simulate both OpenAI and Google AI failures. Verify the system attempts to use 'Cached Content'. 
    c. Simulate failure of all three sources. Verify a specific error is logged and communicated via WebSocket.
4. **WebSocket Error Communication:** For each error scenario (DB down, all AI services down, insufficient questions with no successful fallback), use a WebSocket client to verify that the correct, structured error messages/events are received as defined in the error handling protocol.
5. **System Stability:** Induce various combinations of failures to ensure no unhandled exceptions crash the application. Verify that logs provide clear and actionable information for each error scenario.
