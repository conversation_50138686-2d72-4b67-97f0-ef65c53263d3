# Task ID: 18
# Title: Implement Distribution Enforcement and Balancing in Question Pool Selection
# Status: pending
# Dependencies: 5, 9, 16
# Priority: medium
# Description: Implement algorithms to enforce specified distributions of difficulty levels (e.g., 20% Easy, 60% Medium, 20% Advanced) and balance question types during random question selection from the pool. This includes weighted selection, diversity mechanisms, and a quality validation pipeline.
# Details:
This task involves enhancing the `QuestionPoolService`, specifically the `getRandomQuestions` method (developed in Task 16), to implement sophisticated distribution enforcement and balancing algorithms.
Key implementation aspects:
1.  **Difficulty Level Distribution:** Modify `getRandomQuestions` to accept or retrieve configuration for target difficulty level distributions (e.g., 20% Easy, 60% Medium, 20% Advanced). Implement logic within the MongoDB aggregation pipeline or in post-processing to select questions that meet these distribution targets. The `Question` model (Task 5) must have a `difficultyLevel` field (e.g., 'Easy', 'Medium', 'Advanced').
2.  **Question Type Balancing:** Implement mechanisms to ensure a balanced mix of question types based on configurable parameters or heuristics. The `Question` model (Task 5) must have a `questionType` field.
3.  **Weighted Selection Mechanisms:** Introduce weighting factors in the selection process, potentially based on achieving desired distributions, question quality, or recency.
4.  **Diversity Algorithms:** Implement strategies to prevent excessive repetition of questions. Consider adding fields like `lastSelectedTimestamp` or `selectionFrequency` to the `Question` model (ensure Task 5 accounts for this or update as part of this task). The selection algorithm should penalize or deprioritize recently or frequently selected questions.
5.  **Quality Validation Pipeline Integration:** After initial selection, pass questions through a quality validation pipeline utilizing the `ContentValidationService` (from Task 9) for educational appropriateness checks (as per Phase 3 specifications). Questions failing validation should be handled according to defined rules.
6.  **Configuration:** Define how distribution rules, balancing parameters, and diversity settings will be configured (e.g., global settings, per-request parameters).
7.  **Fallback Strategies:** Define behavior if the pool cannot satisfy requested distributions or diversity criteria (e.g., best effort, logging, error handling).

# Test Strategy:
1.  **Unit Tests:** Test `DistributionLogic` for difficulty level percentages, `QuestionTypeBalancingLogic`, `WeightedSelection` for correct prioritization, and `DiversityAlgorithm` for minimized repetition. Mock `ContentValidationService` to test `QualityValidationIntegration`.
2.  **Integration Tests (within `QuestionPoolService`):** Test `getRandomQuestions` with a seeded MongoDB test database. Verify adherence to distribution/balancing parameters (including the new difficulty level distributions), diversity rule enforcement, and actual `ContentValidationService` integration.
3.  **Scenario Tests:** Simulate generating multiple worksheets to assess overall distribution (including difficulty levels) and diversity. Test fallback strategies with insufficient or skewed pool data.
4.  **Configuration Testing:** Verify that changes in configuration for distributions (including difficulty levels), balancing, and diversity are correctly applied by the selection algorithms.
