{"tasks": [{"id": 1, "title": "Setup NestJS Project and Dependencies", "description": "Set up the core NestJS application structure, configure TypeScript, and install initial dependencies including testing frameworks.", "details": "Initialize a new NestJS project using the NestJS CLI (v10+ recommended). Configure TypeScript 5.7.3. Install core dependencies: `@nestjs/core`, `@nestjs/common`, `@nestjs/platform-express`. Install testing dependencies: `jest` 29.7.0, `supertest` 7.0.0, `@types/jest`, `@types/supertest`. Configure Jest for TypeScript. Set up basic project structure with modules for core functionalities.", "testStrategy": "Verify project structure, successful dependency installation, and basic Jest configuration by running a simple test case.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 2, "title": "Configure MongoDB and Initial Models", "description": "Configure database connections for MongoDB using Mongoose and set up initial data models.", "details": "Install Mongoose 8.13.2 (`mongoose`, `@nestjs/mongoose`). Configure the MongoDB connection string in the NestJS application (e.g., using `ConfigModule`). Define a basic Mongoose schema and model (e.g., for a 'User' or 'Setting') to test the connection. While TypeORM 0.3.22 is mentioned, prioritize Mongoose as the primary ORM for MongoDB as per the PRD's explicit mention.", "testStrategy": "Implement a simple service to connect to MongoDB and perform a basic operation (e.g., create/find a dummy record). Verify successful connection and operation via logs or a test endpoint.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 3, "title": "Implement Basic User Authentication and Authorization", "description": "Implement basic user authentication and authorization using JWT.", "details": "Install authentication dependencies: `@nestjs/passport`, `passport`, `passport-jwt`, `@nestjs/jwt`, `jsonwebtoken`. Implement a `AuthModule` with `JwtStrategy`. Create endpoints for user registration and login. Generate and return JWT tokens upon successful authentication. Implement basic guards (`@nestjs/common` `AuthGuard`) to protect routes. Define basic user roles (e.g., Teacher, <PERSON><PERSON>) and implement role-based authorization using `@nestjs/common` `CanActivate` or a custom decorator.", "testStrategy": "Write integration tests using Supertest to verify user registration, login with correct/incorrect credentials, and access to protected routes with/without a valid JWT.", "priority": "high", "dependencies": [1, 2], "status": "done", "subtasks": []}, {"id": 4, "title": "Integrate Basic AI Services (OpenAI, Google AI)", "description": "Integrate OpenAI and Google Generative AI services and create a service to handle API calls.", "details": "Install AI SDKs: `openai` 4.94.0, `@google/generative-ai` 0.24.0. Create an `AiModule` and an `AiService`. Configure API keys securely (e.g., using `ConfigModule`). Implement methods in `AiService` to make basic text generation calls to both OpenAI and Google AI APIs. Implement basic error handling and logging for API calls. Consider a simple factory or strategy to switch between providers.", "testStrategy": "Write unit tests for the `AiService` methods, mocking the external API calls to ensure correct request parameters are formed and responses are handled. Write integration tests to make actual calls to verify connectivity and basic functionality (using test API keys if available).", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 5, "title": "Define Educational Content Data Models", "description": "Define Mongoose schemas and models for the educational content hierarchy (Subject, Topic, Question, etc.).", "details": "Define Mongoose schemas for `Subject`, `Topic`, `Question`, `Worksheet`, etc. Model the hierarchy: `Topic` belongs to `Subject`, `Subject` can have a `parentSubject`. Include fields for question types, difficulty levels, cognitive complexity, curriculum alignment metadata. Establish relationships between models (e.g., `Worksheet` contains `Question` references). Use Mongoose features like population for querying related data.", "testStrategy": "Write unit tests for each schema to ensure correct field types, required fields, and relationships are defined. Use Mongoose model methods in tests to create and query documents, verifying data integrity and relationships.", "priority": "high", "dependencies": [2], "status": "done", "subtasks": []}, {"id": 6, "title": "Develop Prompt Engineering System", "description": "Develop a system for building dynamic and context-aware prompts for AI content generation.", "details": "Create a `PromptService` that takes parameters like subject, topic, cognitive level, question type, difficulty, and curriculum context (retrieved from DB via models defined in Task 5). Implement logic to construct well-structured prompts for the AI services (OpenAI, Google AI) using prompt templates. Research and apply best practices for educational content prompt engineering (e.g., specifying output format, tone, target age group).", "testStrategy": "Write unit tests for the `PromptService` to verify that prompts are correctly generated based on various input parameters and combinations. Test edge cases and different content types.", "priority": "high", "dependencies": [4, 5], "status": "done", "subtasks": []}, {"id": 7, "title": "Implement Question Type Strategy Pattern", "description": "Implement the Strategy pattern to handle different question types.", "details": "Define a TypeScript interface (e.g., `IQuestionGeneratorStrategy`) with a method like `generate(prompt: string, options: any): Promise<Question>`. Create concrete classes implementing this interface for different question types (e.g., `MultipleChoiceStrategy`, `FillInBlankStrategy`). This promotes modularity and allows adding new question types easily.", "testStrategy": "Write unit tests to ensure the interface is correctly defined and that concrete strategy classes adhere to the interface contract. Test basic instantiation of strategy classes.", "priority": "medium", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 8, "title": "Build Question Type Factories", "description": "Create factories to select and instantiate the correct question generation strategy based on input.", "details": "Create a `QuestionStrategyFactory` service that takes the desired question type as input and returns the appropriate strategy implementation (e.g., an instance of `MultipleChoiceStrategy`). This factory will use the prompt generated by the `PromptService` (Task 6) and the strategies defined in Task 7. Inject necessary dependencies (like the `AiService`) into the strategies via the factory or dependency injection.", "testStrategy": "Write unit tests for the `QuestionStrategyFactory` to verify that it returns the correct strategy instance for each supported question type input. Test handling of unsupported types.", "priority": "high", "dependencies": [6, 7], "status": "done", "subtasks": []}, {"id": 9, "title": "Implement Basic Content Validation", "description": "Implement basic validation logic for generated educational content.", "details": "Create a `ContentValidationService`. Implement basic validation rules based on PRD requirements: age-appropriateness heuristics (e.g., vocabulary complexity), basic cultural sensitivity checks (initial simple checks), format validation (e.g., ensuring MathML is present for math questions, checking structure for MCQs). This service will be used after AI generation.", "testStrategy": "Write unit tests for the `ContentValidationService` with various inputs (valid and invalid content examples) to ensure validation rules are applied correctly and the service returns the expected validation status or errors.", "priority": "medium", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 10, "title": "Develop Basic Worksheet Generation Workflow", "description": "Develop the core workflow for generating a worksheet using AI-powered content generation.", "details": "Create a `WorksheetService` and an API endpoint (e.g., POST /worksheets/generate). This endpoint will receive parameters (subject, topic, difficulty, number of questions, question types, etc.). The service will orchestrate the process: use the `PromptService` (Task 6) to get prompts, use the `QuestionStrategyFactory` (Task 8) to get strategies, call the strategies to generate questions via the `AiService` (Task 4), validate generated content using `ContentValidationService` (Task 9), and save the generated `Worksheet` and `Question` data to the database (Task 5).", "testStrategy": "Write integration tests using Supertest for the worksheet generation endpoint. Mock external AI calls if necessary, but test the full internal flow: receiving parameters, calling services, saving to DB. Verify that a worksheet and associated questions are created in the database with the correct structure.", "priority": "high", "dependencies": [8, 9], "status": "done", "subtasks": []}, {"id": 11, "title": "Integrate Vector Databases (Qdrant, Pinecone)", "description": "Integrate Qdrant and Pinecone vector databases into the application.", "details": "Install client libraries for Qdrant and Pinecone (e.g., `@qdrant/qdrant-js`, `@pinecone-database/pinecone`). Create a `VectorDbModule` and a `VectorDbService`. Configure connections to both databases securely. Implement basic methods for creating collections/indexes and inserting vectors. Decide on a primary vector DB for initial implementation based on research (e.g., Qdrant for self-hosting flexibility, Pinecone for managed service ease) or implement a facade pattern to support both.", "testStrategy": "Write integration tests for the `VectorDbService` to connect to the databases (using test instances or mocks), create a dummy collection/index, and attempt to insert a dummy vector. Verify successful connection and basic operation.", "priority": "medium", "dependencies": [1, 2], "status": "done", "subtasks": []}, {"id": 12, "title": "Implement Embedding Strategies and Semantic Search", "description": "Implement embedding generation and semantic search capabilities using the integrated vector databases.", "details": "Use an embedding model (e.g., OpenAI's `text-embedding-ada-002` via the `openai` SDK from Task 4, or a dedicated embedding library/service) to generate vector embeddings for educational content (curriculum text, sample questions). Implement logic in `VectorDbService` to index these embeddings into Qdrant/Pinecone collections (Task 11). Implement a semantic search method that takes a query, generates its embedding, and queries the vector database for similar content vectors. Implement basic query expansion techniques.", "testStrategy": "Write integration tests: generate embeddings for sample texts, index them in the vector DB. Perform semantic search queries with related texts and verify that the expected similar items are returned (within reasonable similarity thresholds).", "priority": "medium", "dependencies": [4, 11], "status": "done", "subtasks": []}, {"id": 13, "title": "Set up BullMQ for Background Processing", "description": "Set up BullMQ for background job processing.", "details": "Install BullMQ 5.49.2 (`bullmq`). Install Redis client (`ioredis` recommended). Configure a Redis connection for BullMQ (can reuse the connection from Task 2 if applicable, or set up a dedicated one). Create a basic BullMQ queue (e.g., `worksheetGenerationQueue`). Implement a simple job producer to add jobs to the queue and a basic worker to process them. Integrate this with NestJS using `@nestjs/bullmq`.", "testStrategy": "Write integration tests to verify that jobs can be successfully added to the BullMQ queue and that a basic worker can pick up and process a job. Monitor Redis to confirm queue state changes.", "priority": "medium", "dependencies": [1, 2], "status": "done", "subtasks": []}, {"id": 14, "title": "Implement Real-time Collaboration Infrastructure (Socket.IO)", "description": "Implement the basic infrastructure for real-time collaboration using Socket.IO.", "details": "Install Socket.IO 4.8.1 (`socket.io`, `@nestjs/websockets`, `@nestjs/platform-socket.io`). Create a NestJS WebSocket Gateway (e.g., `CollaborationGateway`). Configure the gateway to listen for connections and basic events (e.g., `joinRoom`, `sendMessage`). Implement basic room management logic. This sets up the foundation for real-time features.", "testStrategy": "Write integration tests using a Socket.IO client library in the test suite. Connect to the WebSocket gateway, join a room, send a message, and verify that the message is received by other clients in the same room (simulated in the test).", "priority": "medium", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 15, "title": "Develop Basic Question Pool Management", "description": "Develop the basic system for storing, retrieving, and managing questions in a pool.", "details": "Use the `Question` model defined in Task 5. Create a `QuestionPoolService` and API endpoints (e.g., GET /questions, POST /questions, GET /questions/:id). Implement basic CRUD operations for questions in the pool. Include fields for metadata relevant to the pool (e.g., source, quality score). Implement a basic retrieval method to fetch questions based on criteria (subject, topic, type).", "testStrategy": "Write integration tests using Supertest for the question pool API endpoints. Test creating, retrieving (single and list), updating, and deleting questions. Verify data persistence and retrieval accuracy.", "priority": "medium", "dependencies": [5], "status": "done", "subtasks": []}, {"id": 16, "title": "Implement Advanced Random Question Retrieval in QuestionPoolService", "description": "Implement the `getRandomQuestions` method in `QuestionPoolService` using a MongoDB aggregation pipeline for randomized question selection, supporting comprehensive filtering (including by `difficultyLevel`) and distribution rule enforcement.", "status": "done", "dependencies": [2, 5, 15], "priority": "medium", "details": "Define the `getRandomQuestions` method within the existing `QuestionPoolService` (from Task 15). The method should accept parameters for filtering: `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, and `count` (number of questions to retrieve). Implement the core logic using a MongoDB aggregation pipeline. Utilize the `$match` stage for applying all specified filters based on the `Question` model fields (defined in Task 5), handling optional filters (including `difficultyLevel`). Employ the `$sample` stage for true randomization of the matched questions, selecting the desired `count`. Incorporate logic to enforce distribution rules for question types and difficulty levels, referring to the 'random question pool selection feature documentation' for specifics; this may involve multiple aggregation queries or post-processing. Implement robust error handling, including input validation, handling no-match scenarios, and database errors. Consider fallback mechanisms as per documentation. The method should return a list of `Question` objects or a structured response. Leverage Mongoose models (Task 5) and MongoDB connection (Task 2).", "testStrategy": "Unit Tests: Create comprehensive unit tests for `QuestionPoolService.getRandomQuestions`. Mock MongoDB interactions to test aggregation pipeline construction and logic. Test with various filter combinations, edge cases (0 questions, more questions than available), and verify `$sample` usage. Integration Tests (against a test database): Populate a test MongoDB with diverse questions. Test filtering by `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, and combined filters. Verify correct `count` and randomization across multiple calls. Distribution Rules Testing: Set up test data and call `getRandomQuestions` to trigger distribution rules. Verify adherence to question type and difficulty level distributions. Error Handling Tests: Test with invalid filter parameters and no-match scenarios, ensuring appropriate responses. Fallback Mechanism Tests: If implemented, test scenarios triggering fallback behavior.", "subtasks": []}, {"id": 17, "title": "Enhance WorksheetGenerateConsumer with Hybrid Question Sourcing (Pool & AI)", "description": "Modify `WorksheetGenerateConsumer` to implement a hybrid question sourcing strategy, prioritizing questions from a predefined pool and using AI generation as a fallback. This includes configuration options for question source selection and maintaining existing progress tracking and WebSocket update functionalities.", "details": "1. **Modify `WorksheetGenerateConsumer`**: Update the consumer logic to integrate with `QuestionPoolService` (Task 16) for fetching questions and with `AiService` (Task 4) and `QuestionStrategyFactory` (Task 8) for AI question generation.\n2. **Implement Hybrid Strategy (Pool-First, AI Fallback)**: First, attempt to retrieve questions from the pool using `QuestionPoolService.getRandomQuestions`. If insufficient, calculate the deficit and trigger AI question generation for the remaining questions using appropriate strategies and AI services. Apply filters (subject, topic, type, difficulty, etc.) as specified.\n3. **Configuration for Question Source**: Introduce a configuration parameter in the worksheet generation request or consumer settings to specify the question source: `POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST` (default). \n4. **Question Type Mapping and Validation**: Ensure seamless mapping between requested question types and those available/generated. Utilize `QuestionStrategyFactory` (Task 8) for AI generation of specific types. Integrate `ContentValidationService` (Task 9) to validate all questions.\n5. **Maintain Existing Functionality**: Ensure existing progress tracking mechanisms and WebSocket updates (Task 14) remain functional.\n6. **Error Handling and Logging**: Implement robust error handling for pool issues, AI API failures, or validation failures. Add detailed logging for the question sourcing process.", "testStrategy": "1. **Unit Tests**: Test `WorksheetGenerateConsumer` logic for each source configuration (`POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST`). Mock dependencies (`QuestionPoolService`, AI services, `QuestionStrategyFactory`) to verify correct calls and logic for deficit calculation and AI fallback. Test type mapping and validation integration.\n2. **Integration Tests**: Test end-to-end flow with `WorksheetGenerateConsumer` in BullMQ (Task 13), interacting with `QuestionPoolService` (Task 16) and `AiService` (Task 4). Test scenarios: pool has enough questions, pool has some questions (triggering AI fallback), pool has no relevant questions.\n3. **Functional Tests**: Trigger worksheet generation jobs with different source configurations. Verify generated worksheets contain questions from expected sources, correct number/types of questions, and that content validation (Task 9) is applied. Verify progress tracking and WebSocket updates (Task 14) function correctly. Check logs for sourcing details.", "status": "pending", "dependencies": [4, 8, 9, 13, 14, 16], "priority": "medium", "subtasks": []}, {"id": 18, "title": "Implement Distribution Enforcement and Balancing in Question Pool Selection", "description": "Implement algorithms to enforce specified distributions of difficulty levels (e.g., 20% Easy, 60% Medium, 20% Advanced) and balance question types during random question selection from the pool. This includes weighted selection, diversity mechanisms, and a quality validation pipeline.", "status": "pending", "dependencies": [5, 9, 16], "priority": "medium", "details": "This task involves enhancing the `QuestionPoolService`, specifically the `getRandomQuestions` method (developed in Task 16), to implement sophisticated distribution enforcement and balancing algorithms.\nKey implementation aspects:\n1.  **Difficulty Level Distribution:** Modify `getRandomQuestions` to accept or retrieve configuration for target difficulty level distributions (e.g., 20% Easy, 60% Medium, 20% Advanced). Implement logic within the MongoDB aggregation pipeline or in post-processing to select questions that meet these distribution targets. The `Question` model (Task 5) must have a `difficultyLevel` field (e.g., 'Easy', 'Medium', 'Advanced').\n2.  **Question Type Balancing:** Implement mechanisms to ensure a balanced mix of question types based on configurable parameters or heuristics. The `Question` model (Task 5) must have a `questionType` field.\n3.  **Weighted Selection Mechanisms:** Introduce weighting factors in the selection process, potentially based on achieving desired distributions, question quality, or recency.\n4.  **Diversity Algorithms:** Implement strategies to prevent excessive repetition of questions. Consider adding fields like `lastSelectedTimestamp` or `selectionFrequency` to the `Question` model (ensure Task 5 accounts for this or update as part of this task). The selection algorithm should penalize or deprioritize recently or frequently selected questions.\n5.  **Quality Validation Pipeline Integration:** After initial selection, pass questions through a quality validation pipeline utilizing the `ContentValidationService` (from Task 9) for educational appropriateness checks (as per Phase 3 specifications). Questions failing validation should be handled according to defined rules.\n6.  **Configuration:** Define how distribution rules, balancing parameters, and diversity settings will be configured (e.g., global settings, per-request parameters).\n7.  **Fallback Strategies:** Define behavior if the pool cannot satisfy requested distributions or diversity criteria (e.g., best effort, logging, error handling).", "testStrategy": "1.  **Unit Tests:** Test `DistributionLogic` for difficulty level percentages, `QuestionTypeBalancingLogic`, `WeightedSelection` for correct prioritization, and `DiversityAlgorithm` for minimized repetition. Mock `ContentValidationService` to test `QualityValidationIntegration`.\n2.  **Integration Tests (within `QuestionPoolService`):** Test `getRandomQuestions` with a seeded MongoDB test database. Verify adherence to distribution/balancing parameters (including the new difficulty level distributions), diversity rule enforcement, and actual `ContentValidationService` integration.\n3.  **Scenario Tests:** Simulate generating multiple worksheets to assess overall distribution (including difficulty levels) and diversity. Test fallback strategies with insufficient or skewed pool data.\n4.  **Configuration Testing:** Verify that changes in configuration for distributions (including difficulty levels), balancing, and diversity are correctly applied by the selection algorithms.", "subtasks": []}, {"id": 19, "title": "Optimize Random Question Pool Selection Performance", "description": "Implement a caching strategy for random question pool results using WorksheetDocumentCacheService, optimize database query performance via indexing on key fields (subject, parentSubject, grade, type, status, difficultyLevel), manage MongoDB connection pooling, and establish performance monitoring with metrics collection for the question selection process.", "status": "pending", "dependencies": [18, 5, 2], "priority": "medium", "details": "This task focuses on enhancing the performance and scalability of the random question selection mechanism from the question pool.\n\n**1. Caching Strategy Implementation (using `WorksheetDocumentCacheService`):**\n   - Target the `getRandomQuestions` method in `QuestionPoolService` (from Task 16, enhanced by Task 18) for caching.\n   - Integrate with the `WorksheetDocumentCacheService` (assume available or provided). Define any necessary adaptations if question objects require special handling by the cache service.\n   - Develop a robust cache key generation strategy. Keys must uniquely identify a query based on all its parameters: `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, `count`, and any distribution rules (from Task 18).\n   - Implement logic to populate the cache with successful, non-empty results from `getRandomQuestions`.\n   - Implement cache invalidation strategies:\n     - **Time-To-Live (TTL):** Configure suitable TTLs for cached question sets based on content volatility.\n     - **Event-driven (Optional):** If feasible, explore invalidating or updating specific cache entries when underlying questions in the pool are created, updated, or deleted, particularly if they match criteria of cached queries.\n\n**2. Database Indexing Optimization (MongoDB):**\n   - Analyze query patterns from the MongoDB aggregation pipeline in `getRandomQuestions` (Task 16/18).\n   - Identify and create optimal compound indexes on the `questions` collection to support filtering, sorting, and aggregation stages. Fields for consideration include `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, and `status`.\n   - Example indexes (to be refined based on actual query analysis):\n     - `{ \"subjectId\": 1, \"gradeLevel\": 1, \"questionType\": 1, \"status\": 1 }`\n     - `{ \"parentSubjectId\": 1, \"gradeLevel\": 1, \"status\": 1 }`\n     - `{ \"difficultyLevel\": 1, \"status\": 1, \"questionType\": 1 }`\n   - Utilize MongoDB's `explain(\"executionStats\")` command extensively to verify index usage (e.g., presence of `IXSCAN` stages, minimized `docsExamined`) and effectiveness for various query combinations.\n\n**3. Connection Pooling Management (MongoDB Driver):**\n   - Review and fine-tune MongoDB driver connection pool settings within the NestJS application's database configuration.\n   - Adjust parameters like `maxPoolSize`, `minPoolSize`, `maxIdleTimeMS`, and `waitQueueTimeoutMS` to align with expected application load, concurrency, and performance requirements.\n   - The goal is to ensure efficient connection reuse, minimize connection latency, and prevent connection exhaustion under peak loads.\n\n**4. Performance Monitoring and Metrics Collection:**\n   - Integrate a metrics collection library (e.g., `prom-client` for Prometheus) or leverage an APM tool compatible with NestJS.\n   - Instrument the `QuestionPoolService.getRandomQuestions` method to capture and expose key performance indicators (KPIs):\n     - Overall execution time (average, p95, p99).\n     - Cache hit rate and miss rate for `WorksheetDocumentCacheService` related to question pool queries.\n     - Database query execution time (if separable).\n     - Number of questions requested versus number of questions returned.\n     - Error rates specific to the question selection operation.\n   - Expose these metrics (e.g., via a `/metrics` endpoint for Prometheus scraping) or ensure they are pushed to a centralized monitoring dashboard.\n   - Establish baseline performance targets and consider configuring alerts for significant deviations (e.g., sustained high latency, critically low cache hit rate).", "testStrategy": "**1. Caching Verification:**\n   - **Unit Tests:** Validate the cache key generation logic for correctness and uniqueness under various input parameter combinations.\n   - **Integration Tests:**\n     - Invoke `getRandomQuestions` multiple times with identical parameters. Assert that subsequent calls are significantly faster and (by mocking or inspecting the cache service) confirm that results are served from the cache.\n     - Verify that altering any query parameter results in a cache miss and triggers a fresh database query.\n     - Test cache invalidation: Modify a question in the database that should affect a cached result set. Re-query and assert that fresh data is returned. Verify TTL-based expiration.\n\n**2. Database Indexing Verification:**\n   - **Baseline Measurement:** Before applying new indexes, execute representative `getRandomQuestions` queries and record their performance using `explain(\"executionStats\")` (note `docsExamined`, execution time, stages used).\n   - **Post-Indexing Validation:** After creating indexes, re-run the same queries. Use `explain(\"executionStats\")` to confirm that the new indexes are being utilized effectively (e.g., `IXSCAN` is used, `docsExamined` is reduced, query plans are more efficient).\n   - **Load Testing:** Conduct targeted load tests on the `getRandomQuestions` endpoint before and after indexing to quantify the improvement in query response times and throughput under concurrent load.\n\n**3. Connection Pooling Verification:**\n   - **Monitoring:** During load tests, monitor MongoDB server connection statistics (`db.serverStatus().connections`) to observe the number of active, idle, and available connections. Ensure these numbers stay within the configured pool limits and that the pool behaves as expected (e.g., connections are reused).\n   - **Stress Test:** Simulate high concurrency scenarios targeting `getRandomQuestions` to verify that the application manages database connections gracefully without connection timeout errors or pool exhaustion issues.\n\n**4. Performance Monitoring Verification:**\n   - **Metrics Validation:** Manually trigger various scenarios for `getRandomQuestions` (e.g., cache hits, cache misses, successful queries, queries resulting in errors). Verify that all configured metrics (latency, cache hit/miss ratio, error counts, etc.) are accurately generated and reported to the monitoring system or endpoint.\n   - **Dashboard/Alerting Check (if applicable):** Confirm that metrics are correctly displayed on dashboards and that alerts trigger based on predefined thresholds when simulated conditions meet alert criteria.\n\n**5. Overall System Performance:**\n   - **End-to-End Load Testing:** Conduct comprehensive load tests simulating realistic user traffic patterns that heavily utilize the `getRandomQuestions` functionality.\n   - **Comparative Analysis:** Measure and compare key performance metrics (overall throughput, response time percentiles, error rates) before and after all optimizations (caching, indexing, connection pooling) are implemented to demonstrate the cumulative positive impact.", "subtasks": []}, {"id": 20, "title": "Implement Configuration Management for Random Question Pool Selection", "description": "Implement a configuration management system for the random question pool selection feature. This includes managing environment variables, feature flags for different selection strategies, and defining a `WorksheetGenerationOptions` interface with validation and default values.", "details": "1. **Setup Configuration Module**:\n    *   Utilize NestJS's `@nestjs/config` module.\n    *   Create a typed configuration file (e.g., `question-pool.config.ts`) using `registerAs` to define and export the configuration schema. Load this using `ConfigModule.forRoot({ load: [questionPoolConfig] })`.\n    *   Ensure this configuration is globally available or imported into relevant modules.\n2. **Environment Variables & Typed Configuration**:\n    *   Define and manage the following settings, loadable from environment variables, with clear defaults in `question-pool.config.ts`:\n        *   `QUESTION_POOL_ENABLED`: boolean (default: `true`). Controls overall usage of the question pool.\n        *   `DEFAULT_SELECTION_STRATEGY`: string enum (`'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`; default: `'hybrid'`). Specifies the default strategy for question sourcing.\n        *   `MIN_POOL_QUESTIONS_THRESHOLD`: number (default: `10`). Minimum number of questions required in the pool for it to be considered a primary source under certain strategies.\n3. **Feature Flags for Selection Strategies (within Config)**:\n    *   Extend the typed configuration to manage feature flags for selection strategies:\n        *   `allow_pool_only_strategy`: boolean (default: `true`)\n        *   `allow_ai_only_strategy`: boolean (default: `true`)\n        *   `allow_hybrid_strategy`: boolean (default: `true`)\n        *   `allow_mixed_strategy`: boolean (default: `true`)\n    *   These flags will determine which strategies are permissible for selection when processing `WorksheetGenerationOptions`.\n4. **`WorksheetGenerationOptions` DTO Definition**:\n    *   Define a Data Transfer Object (DTO) class, `WorksheetGenerationOptionsDto`, for options related to worksheet generation, particularly those affecting question sourcing. This DTO will be used for input validation in controllers or message handlers.\n    *   Properties to include (all optional, with defaults applied by consuming services based on global config):\n        *   `selectionStrategy?: string` (e.g., `'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`)\n        *   `useQuestionPoolOverride?: boolean`\n        *   `minPoolQuestionsRequired?: number`\n    *   **Validation**: Use `class-validator` decorators within the DTO for robust validation:\n        *   `@IsOptional() @IsEnum(['pool-only', 'ai-only', 'hybrid', 'mixed']) selectionStrategy?: string;` (further validation against active feature flags will be done in service layer).\n        *   `@IsOptional() @IsBoolean() useQuestionPoolOverride?: boolean;`\n        *   `@IsOptional() @IsInt() @Min(0) minPoolQuestionsRequired?: number;`\n5. **Configuration Service/Injection**:\n    *   Ensure the typed configuration (e.g., `QuestionPoolConfigType` from `question-pool.config.ts`) is injectable into services like `QuestionPoolService`, `WorksheetGenerateConsumer`, etc., using `@Inject(questionPoolConfig.KEY)`.\n    *   Consuming services will use these global configurations as defaults and then apply overrides from `WorksheetGenerationOptionsDto` if provided and valid.", "testStrategy": "1. **Unit Tests for Configuration Loading (`question-pool.config.ts`)**:\n    *   Verify correct loading of environment variables (e.g., using `process.env` mocks).\n    *   Test that default values are correctly applied when specific environment variables are not set.\n    *   Test that environment variables correctly override the default values for `QUESTION_POOL_ENABLED`, `DEFAULT_SELECTION_STRATEGY`, and `MIN_POOL_QUESTIONS_THRESHOLD`.\n2. **Unit Tests for Feature Flag Logic (within Config or Consuming Service)**:\n    *   Verify that feature flags for selection strategies (`allow_pool_only_strategy`, etc.) are correctly read from the configuration.\n    *   Test logic that checks if a requested strategy in `WorksheetGenerationOptionsDto` is permitted based on these flags.\n3. **Unit Tests for `WorksheetGenerationOptionsDto` Validation**:\n    *   Use `class-validator`'s `validate` function to test the DTO.\n    *   Test with valid inputs for all properties.\n    *   Test with invalid inputs: incorrect enum for `selectionStrategy`, non-boolean for `useQuestionPoolOverride`, non-integer or negative for `minPoolQuestionsRequired`.\n    *   Verify that validation errors are correctly reported.\n4. **Integration Tests for Config Injection and Usage**:\n    *   Create a test NestJS module that registers the `ConfigModule` with `question-pool.config.ts`.\n    *   Inject the typed configuration into a simple test service.\n    *   Assert that the test service receives the correct configuration values based on mock environment settings.\n5. **Service-Level Tests (Focus on Config Consumption)**:\n    *   For services like `WorksheetGenerateConsumer` or `QuestionPoolService` (or simplified versions for testing this aspect):\n        *   Verify they correctly access and utilize `QUESTION_POOL_ENABLED`.\n        *   Verify they use `DEFAULT_SELECTION_STRATEGY` when `WorksheetGenerationOptionsDto.selectionStrategy` is undefined.\n        *   Verify they prioritize `WorksheetGenerationOptionsDto.selectionStrategy` if provided and allowed by feature flags.\n        *   Test the behavior when `WorksheetGenerationOptionsDto.selectionStrategy` is provided but not allowed by feature flags (e.g., error, fallback to default).", "status": "pending", "dependencies": [1, 15], "priority": "medium", "subtasks": []}, {"id": 21, "title": "Implement Comprehensive Error Handling for Random Question Pool Selection", "description": "Implement robust error handling and recovery mechanisms for the random question pool selection process, covering database connectivity, insufficient content, AI service failures, and WebSocket communication.", "details": "1. **MongoDB Connection Retry Logic:** Modify `QuestionPoolService` or the underlying data access layer to implement retry logic for MongoDB operations. Configure for 3 retry attempts with exponential backoff upon connection failures or transient errors during question retrieval. Log each attempt and the final status.\n2. **Insufficient Questions Graceful Degradation & AI Fallback:** Enhance the question sourcing logic (likely in `WorksheetGenerateConsumer` or an orchestrator service) to handle scenarios where the question pool (Task 16) returns fewer questions than requested. If a deficit occurs, trigger AI question generation (using services from Task 4, potentially orchestrated by logic from Task 17) to fulfill the remaining count. This fallback should be configurable (Task 20).\n3. **AI Service Failure Chain:** Implement a resilient AI interaction layer (e.g., a new `AiOrchestrationService`). This service will attempt to generate questions sequentially: first from OpenAI, then upon failure, from Google AI. If both AI services fail, it will attempt to retrieve questions from a 'Cached Content' source as a final fallback. Log the outcome of each attempt and the service ultimately used.\n4. **WebSocket Error Communication:** Integrate with the WebSocket gateway (Task 14) to communicate errors effectively to the client. Define specific error event types (e.g., `worksheet:generation:error`, `database:unavailable`, `ai:service:failed`, `insufficient_questions:no_fallback`) and structured error payloads (error code, message, details). Ensure errors from all parts of the question selection and generation pipeline are caught and propagated correctly.\n5. **Comprehensive Logging:** Implement detailed logging using NestJS `LoggerService` for all error events, retry attempts, fallback invocations, and AI service interactions to facilitate debugging and monitoring.", "testStrategy": "1. **MongoDB Connection Failure:** Simulate MongoDB unavailability (e.g., stop service, invalid connection string). Verify that the system attempts reconnection 3 times with backoff (check logs) and eventually communicates a DB error via WebSocket if unsuccessful.\n2. **Insufficient Questions & AI Fallback:** Configure the question pool with fewer questions than a test request. Verify the system identifies the deficit, logs it, and successfully triggers the AI fallback to generate the missing questions. Mock AI services to confirm they are called with correct parameters. The final question set should meet the requested count.\n3. **AI Service Failure Chain:** \n    a. Simulate OpenAI API failure (e.g., mock endpoint to return error, invalid API key). Verify the system logs the failure and automatically attempts to use Google AI. \n    b. Simulate both OpenAI and Google AI failures. Verify the system attempts to use 'Cached Content'. \n    c. Simulate failure of all three sources. Verify a specific error is logged and communicated via WebSocket.\n4. **WebSocket Error Communication:** For each error scenario (DB down, all AI services down, insufficient questions with no successful fallback), use a WebSocket client to verify that the correct, structured error messages/events are received as defined in the error handling protocol.\n5. **System Stability:** Induce various combinations of failures to ensure no unhandled exceptions crash the application. Verify that logs provide clear and actionable information for each error scenario.", "status": "pending", "dependencies": [4, 14, 16, 17, 19, 20], "priority": "medium", "subtasks": []}, {"id": 22, "title": "Implement Monitoring and Analytics System for Question Pool Selection", "description": "Develop a comprehensive monitoring and analytics system to track key performance, quality, and utilization metrics for the random question pool selection process. This includes an admin dashboard for visualizing these metrics.", "details": "1. **Metric Collection Service:** Create a `MonitoringModule` and `PoolMonitoringService`. Integrate with `QuestionPoolService` (Task 16), `WorksheetGenerateConsumer` (Task 17), `WorksheetDocumentCacheService` (Task 19), and `ContentValidationService` (Task 9) to capture relevant events and data. Implement mechanisms to log/store metrics, potentially in MongoDB collections or a time-series database.\n2. **Key Metrics Implementation:** Track and calculate: \n    a. **Pool Utilization Rate:** Percentage of unique questions used from the pool over time.\n    b. **Question Reuse Frequency:** How often individual questions are selected.\n    c. **Generation Time Comparison:** Average time for question selection from pool vs. AI generation (from Task 17).\n    d. **Validation Success Rate:** Success/failure rate of content validation (from Task 9).\n    e. **Distribution Adherence:** How well selected questions meet specified cognitive level/type distributions (from Task 18).\n    f. **Query Response Times:** Average/percentile response times for `QuestionPoolService.getRandomQuestions` (from Task 16/19).\n    g. **Cache Hit/Miss Ratios:** Track hit/miss ratios for the question pool cache (from Task 19).\n3. **Admin Dashboard Development:** Create a new secured section in the admin interface (requires Task 3 for authentication/authorization). Display collected metrics using charts and tables. Dashboard components should include an overview of key metrics, time-series charts for utilization, reuse, generation times, response times, validation success rates, distribution adherence visualization, and cache performance statistics.\n4. **Data Storage for Metrics:** Define schemas for storing aggregated metrics. Implement data aggregation jobs (e.g., hourly, daily) if raw event logging is used.", "testStrategy": "1. **Unit Tests:** Verify `PoolMonitoringService` for correct metric calculation logic using mocked dependent services.\n2. **Integration Tests:** Ensure events from `QuestionPoolService`, `WorksheetGenerateConsumer`, etc., are correctly captured and stored by the monitoring service. Test data aggregation if implemented.\n3. **End-to-End Tests (Admin Dashboard):** \n    a. Log in as an admin user (Task 3).\n    b. Navigate to the monitoring dashboard.\n    c. Trigger actions that generate metrics (e.g., request worksheets).\n    d. Verify the dashboard displays updated and accurate metrics for: pool utilization, question reuse, generation times (pool vs. AI), validation success/failure rates, distribution adherence, query response times, and cache hit/miss ratios.\n4. **Performance Tests:** Ensure metric collection does not significantly degrade the performance of core question selection services.", "status": "pending", "dependencies": [3, 9, 16, 17, 18, 19], "priority": "medium", "subtasks": []}, {"id": 23, "title": "Implement Comprehensive Testing Suite for Random Question Pool Selection Feature", "description": "Develop a comprehensive testing suite covering unit, integration, performance, end-to-end, and validation tests for the random question pool selection feature and its integration into worksheet generation.", "details": "Implement a robust testing suite for the random question pool selection functionality. \n1. **Testing Frameworks**: Utilize <PERSON><PERSON> for unit and integration tests. Employ Playwright or <PERSON><PERSON> for end-to-end tests. For performance testing, use tools like k6 or Artillery.\n2. **Unit Tests**:\n    - `QuestionPoolService.getRandomQuestions()`: Mock MongoDB interactions. Test various filter combinations (subject, grade, type, language, cognitiveLevel), count parameter, edge cases (empty pool, insufficient questions matching criteria), and correct application of distribution rules by verifying inputs to the aggregation pipeline.\n    - Distribution Algorithm Unit Tests: Isolate and test algorithms for weighted selection, diversity mechanisms, cognitive level balancing, and question type balancing as implemented in Task 18.\n3. **Integration Tests**:\n    - `WorksheetGenerateConsumer` with `QuestionPoolService`: Verify correct invocation of `getRandomQuestions` with appropriate `WorksheetGenerationOptions`. Test handling of successful responses, errors, and fallback mechanisms (e.g., AI fallback if pool selection fails, as per Task 17).\n    - `QuestionPoolService` with `WorksheetDocumentCacheService` (Task 19): Test caching logic, including cache hits, misses, and impact on response times.\n    - Interaction with Configuration Management (Task 20): Ensure tests can run with different configurations (e.g., feature flags for selection strategies, different distribution rule settings).\n4. **Performance Tests**:\n    - `QuestionPoolService.getRandomQuestions()` MongoDB Aggregation (Task 16): Measure query execution time and system load under various data volumes and concurrent request scenarios. Identify and report bottlenecks.\n    - Caching Impact (Task 19): Quantify performance improvements due to caching strategies.\n5. **End-to-End (E2E) Tests**:\n    - Simulate complete user workflows for worksheet generation using the hybrid approach (pool selection primary, AI fallback as per Task 17).\n    - Verify the entire flow: API request -> `WorksheetGenerateConsumer` -> `QuestionPoolService` -> (optional AI sourcing via Task 12 if part of hybrid model) -> Worksheet result.\n    - Include scenarios requiring user authentication (Task 3).\n    - If applicable to the workflow, test scenarios involving WebSocket communication for progress updates or result delivery (Task 14), especially for error handling aspects covered in Task 21.\n6. **Validation Tests**:\n    - Educational Content Quality: Based on rules from Task 9, implement automated checks on questions selected from the pool for basic quality markers (e.g., format validity, completeness of required fields).\n    - Cognitive Level Distribution: Verify that batches of selected questions adhere to the specified cognitive level distributions defined in Task 18.\n    - Question Type Distribution: Confirm that selected questions meet the question type balancing rules from Task 18.\n7. **Error Handling Tests** (covering Task 21):\n    - Test graceful degradation and fallback strategies when the pool has insufficient content.\n    - Simulate and verify system behavior during database connectivity issues for `QuestionPoolService`.\n    - Test AI service failure fallbacks if E2E tests cover this part of the hybrid model from Task 17.\n    - Test error handling related to WebSocket communication if part of the E2E workflow or covered by Task 21.", "testStrategy": "1. **Code Review**: All test code (unit, integration, E2E, performance, validation) will be peer-reviewed for correctness, clarity, and comprehensive coverage.\n2. **Test Execution & Passing**: All implemented tests must pass successfully in a dedicated test environment that mirrors production as closely as possible.\n3. **Coverage Reports**: Achieve a minimum of 85% statement and branch coverage for key modules, including `QuestionPoolService`, distribution algorithms, and relevant integration points in `WorksheetGenerateConsumer`, as reported by Jest's coverage tools.\n4. **Performance Test Analysis**: Performance test results, including response times and resource utilization under load, will be documented, analyzed, and compared against predefined performance benchmarks or baselines. Any identified bottlenecks will be reported.\n5. **E2E Test Validation**: E2E test scenarios must successfully simulate and validate critical user workflows for worksheet generation involving random question pool selection and the hybrid sourcing model. Video recordings or detailed logs of E2E test runs should be available for failures.\n6. **Validation Test Verification**: Demonstrate that the implemented validation tests correctly identify adherence to, and violations of, educational content quality standards and distribution rules for cognitive levels and question types.\n7. **CI/CD Integration**: The entire testing suite must be integrated into the CI/CD pipeline, triggering automatically on relevant code changes. The pipeline must pass with all new tests before the task is considered complete.", "status": "pending", "dependencies": [3, 9, 12, 14, 16, 17, 18, 19, 20, 21], "priority": "medium", "subtasks": []}]}