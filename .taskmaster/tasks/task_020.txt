# Task ID: 20
# Title: Implement Configuration Management for Random Question Pool Selection
# Status: pending
# Dependencies: 1, 15
# Priority: medium
# Description: Implement a configuration management system for the random question pool selection feature. This includes managing environment variables, feature flags for different selection strategies, and defining a `WorksheetGenerationOptions` interface with validation and default values.
# Details:
1. **Setup Configuration Module**:
    *   Utilize NestJS's `@nestjs/config` module.
    *   Create a typed configuration file (e.g., `question-pool.config.ts`) using `registerAs` to define and export the configuration schema. Load this using `ConfigModule.forRoot({ load: [questionPoolConfig] })`.
    *   Ensure this configuration is globally available or imported into relevant modules.
2. **Environment Variables & Typed Configuration**:
    *   Define and manage the following settings, loadable from environment variables, with clear defaults in `question-pool.config.ts`:
        *   `QUESTION_POOL_ENABLED`: boolean (default: `true`). Controls overall usage of the question pool.
        *   `DEFAULT_SELECTION_STRATEGY`: string enum (`'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`; default: `'hybrid'`). Specifies the default strategy for question sourcing.
        *   `MIN_POOL_QUESTIONS_THRESHOLD`: number (default: `10`). Minimum number of questions required in the pool for it to be considered a primary source under certain strategies.
3. **Feature Flags for Selection Strategies (within Config)**:
    *   Extend the typed configuration to manage feature flags for selection strategies:
        *   `allow_pool_only_strategy`: boolean (default: `true`)
        *   `allow_ai_only_strategy`: boolean (default: `true`)
        *   `allow_hybrid_strategy`: boolean (default: `true`)
        *   `allow_mixed_strategy`: boolean (default: `true`)
    *   These flags will determine which strategies are permissible for selection when processing `WorksheetGenerationOptions`.
4. **`WorksheetGenerationOptions` DTO Definition**:
    *   Define a Data Transfer Object (DTO) class, `WorksheetGenerationOptionsDto`, for options related to worksheet generation, particularly those affecting question sourcing. This DTO will be used for input validation in controllers or message handlers.
    *   Properties to include (all optional, with defaults applied by consuming services based on global config):
        *   `selectionStrategy?: string` (e.g., `'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`)
        *   `useQuestionPoolOverride?: boolean`
        *   `minPoolQuestionsRequired?: number`
    *   **Validation**: Use `class-validator` decorators within the DTO for robust validation:
        *   `@IsOptional() @IsEnum(['pool-only', 'ai-only', 'hybrid', 'mixed']) selectionStrategy?: string;` (further validation against active feature flags will be done in service layer).
        *   `@IsOptional() @IsBoolean() useQuestionPoolOverride?: boolean;`
        *   `@IsOptional() @IsInt() @Min(0) minPoolQuestionsRequired?: number;`
5. **Configuration Service/Injection**:
    *   Ensure the typed configuration (e.g., `QuestionPoolConfigType` from `question-pool.config.ts`) is injectable into services like `QuestionPoolService`, `WorksheetGenerateConsumer`, etc., using `@Inject(questionPoolConfig.KEY)`.
    *   Consuming services will use these global configurations as defaults and then apply overrides from `WorksheetGenerationOptionsDto` if provided and valid.

# Test Strategy:
1. **Unit Tests for Configuration Loading (`question-pool.config.ts`)**:
    *   Verify correct loading of environment variables (e.g., using `process.env` mocks).
    *   Test that default values are correctly applied when specific environment variables are not set.
    *   Test that environment variables correctly override the default values for `QUESTION_POOL_ENABLED`, `DEFAULT_SELECTION_STRATEGY`, and `MIN_POOL_QUESTIONS_THRESHOLD`.
2. **Unit Tests for Feature Flag Logic (within Config or Consuming Service)**:
    *   Verify that feature flags for selection strategies (`allow_pool_only_strategy`, etc.) are correctly read from the configuration.
    *   Test logic that checks if a requested strategy in `WorksheetGenerationOptionsDto` is permitted based on these flags.
3. **Unit Tests for `WorksheetGenerationOptionsDto` Validation**:
    *   Use `class-validator`'s `validate` function to test the DTO.
    *   Test with valid inputs for all properties.
    *   Test with invalid inputs: incorrect enum for `selectionStrategy`, non-boolean for `useQuestionPoolOverride`, non-integer or negative for `minPoolQuestionsRequired`.
    *   Verify that validation errors are correctly reported.
4. **Integration Tests for Config Injection and Usage**:
    *   Create a test NestJS module that registers the `ConfigModule` with `question-pool.config.ts`.
    *   Inject the typed configuration into a simple test service.
    *   Assert that the test service receives the correct configuration values based on mock environment settings.
5. **Service-Level Tests (Focus on Config Consumption)**:
    *   For services like `WorksheetGenerateConsumer` or `QuestionPoolService` (or simplified versions for testing this aspect):
        *   Verify they correctly access and utilize `QUESTION_POOL_ENABLED`.
        *   Verify they use `DEFAULT_SELECTION_STRATEGY` when `WorksheetGenerationOptionsDto.selectionStrategy` is undefined.
        *   Verify they prioritize `WorksheetGenerationOptionsDto.selectionStrategy` if provided and allowed by feature flags.
        *   Test the behavior when `WorksheetGenerationOptionsDto.selectionStrategy` is provided but not allowed by feature flags (e.g., error, fallback to default).
