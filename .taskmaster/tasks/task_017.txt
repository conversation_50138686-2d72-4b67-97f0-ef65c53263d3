# Task ID: 17
# Title: Enhance WorksheetGenerateConsumer with Hybrid Question Sourcing (Pool & AI)
# Status: pending
# Dependencies: 4, 8, 9, 13, 14, 16
# Priority: medium
# Description: Modify `WorksheetGenerateConsumer` to implement a hybrid question sourcing strategy, prioritizing questions from a predefined pool and using AI generation as a fallback. This includes configuration options for question source selection and maintaining existing progress tracking and WebSocket update functionalities.
# Details:
1. **Modify `WorksheetGenerateConsumer`**: Update the consumer logic to integrate with `QuestionPoolService` (Task 16) for fetching questions and with `AiService` (Task 4) and `QuestionStrategyFactory` (Task 8) for AI question generation.
2. **Implement Hybrid Strategy (Pool-First, AI Fallback)**: First, attempt to retrieve questions from the pool using `QuestionPoolService.getRandomQuestions`. If insufficient, calculate the deficit and trigger AI question generation for the remaining questions using appropriate strategies and AI services. Apply filters (subject, topic, type, difficulty, etc.) as specified.
3. **Configuration for Question Source**: Introduce a configuration parameter in the worksheet generation request or consumer settings to specify the question source: `POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST` (default). 
4. **Question Type Mapping and Validation**: Ensure seamless mapping between requested question types and those available/generated. Utilize `QuestionStrategyFactory` (Task 8) for AI generation of specific types. Integrate `ContentValidationService` (Task 9) to validate all questions.
5. **Maintain Existing Functionality**: Ensure existing progress tracking mechanisms and WebSocket updates (Task 14) remain functional.
6. **Error Handling and Logging**: Implement robust error handling for pool issues, AI API failures, or validation failures. Add detailed logging for the question sourcing process.

# Test Strategy:
1. **Unit Tests**: Test `WorksheetGenerateConsumer` logic for each source configuration (`POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST`). Mock dependencies (`QuestionPoolService`, AI services, `QuestionStrategyFactory`) to verify correct calls and logic for deficit calculation and AI fallback. Test type mapping and validation integration.
2. **Integration Tests**: Test end-to-end flow with `WorksheetGenerateConsumer` in BullMQ (Task 13), interacting with `QuestionPoolService` (Task 16) and `AiService` (Task 4). Test scenarios: pool has enough questions, pool has some questions (triggering AI fallback), pool has no relevant questions.
3. **Functional Tests**: Trigger worksheet generation jobs with different source configurations. Verify generated worksheets contain questions from expected sources, correct number/types of questions, and that content validation (Task 9) is applied. Verify progress tracking and WebSocket updates (Task 14) function correctly. Check logs for sourcing details.
