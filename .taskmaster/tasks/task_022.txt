# Task ID: 22
# Title: Implement Monitoring and Analytics System for Question Pool Selection
# Status: pending
# Dependencies: 3, 9, 16, 17, 18, 19
# Priority: medium
# Description: Develop a comprehensive monitoring and analytics system to track key performance, quality, and utilization metrics for the random question pool selection process. This includes an admin dashboard for visualizing these metrics.
# Details:
1. **Metric Collection Service:** Create a `MonitoringModule` and `PoolMonitoringService`. Integrate with `QuestionPoolService` (Task 16), `WorksheetGenerateConsumer` (Task 17), `WorksheetDocumentCacheService` (Task 19), and `ContentValidationService` (Task 9) to capture relevant events and data. Implement mechanisms to log/store metrics, potentially in MongoDB collections or a time-series database.
2. **Key Metrics Implementation:** Track and calculate: 
    a. **Pool Utilization Rate:** Percentage of unique questions used from the pool over time.
    b. **Question Reuse Frequency:** How often individual questions are selected.
    c. **Generation Time Comparison:** Average time for question selection from pool vs. AI generation (from Task 17).
    d. **Validation Success Rate:** Success/failure rate of content validation (from Task 9).
    e. **Distribution Adherence:** How well selected questions meet specified cognitive level/type distributions (from Task 18).
    f. **Query Response Times:** Average/percentile response times for `QuestionPoolService.getRandomQuestions` (from Task 16/19).
    g. **Cache Hit/Miss Ratios:** Track hit/miss ratios for the question pool cache (from Task 19).
3. **Admin Dashboard Development:** Create a new secured section in the admin interface (requires Task 3 for authentication/authorization). Display collected metrics using charts and tables. Dashboard components should include an overview of key metrics, time-series charts for utilization, reuse, generation times, response times, validation success rates, distribution adherence visualization, and cache performance statistics.
4. **Data Storage for Metrics:** Define schemas for storing aggregated metrics. Implement data aggregation jobs (e.g., hourly, daily) if raw event logging is used.

# Test Strategy:
1. **Unit Tests:** Verify `PoolMonitoringService` for correct metric calculation logic using mocked dependent services.
2. **Integration Tests:** Ensure events from `QuestionPoolService`, `WorksheetGenerateConsumer`, etc., are correctly captured and stored by the monitoring service. Test data aggregation if implemented.
3. **End-to-End Tests (Admin Dashboard):** 
    a. Log in as an admin user (Task 3).
    b. Navigate to the monitoring dashboard.
    c. Trigger actions that generate metrics (e.g., request worksheets).
    d. Verify the dashboard displays updated and accurate metrics for: pool utilization, question reuse, generation times (pool vs. AI), validation success/failure rates, distribution adherence, query response times, and cache hit/miss ratios.
4. **Performance Tests:** Ensure metric collection does not significantly degrade the performance of core question selection services.
